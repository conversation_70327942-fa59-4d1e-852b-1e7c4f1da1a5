import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Image,
  ActivityIndicator,
  Platform,
} from 'react-native';
import { useRouter, Stack } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useThemeColors } from '@/hooks/useThemeColors'; // Import the theme hook
import { useAuthStore } from '@/store/auth-store';
import { useFarmStore } from '@/store/farm-store';
import { FarmStatus } from '@/types/farm';
import { useTranslation } from '@/hooks/useTranslation';
import { Save, MapPin, Building2, Camera, Mic, MicOff, Ruler, FileText } from 'lucide-react-native';
import Input from '@/components/Input';
import Button from '@/components/Button';
import { useToast } from '@/contexts/ToastContext';
import { doc, getDoc, updateDoc } from 'firebase/firestore';
import { firestore } from '@/config/firebase';
import * as ImagePicker from 'expo-image-picker';
import ImageCaptureButtons from '@/components/ImageCaptureButtons';
import GenericDropdown from '@/components/GenericDropdown';
import { FARM_SIZE_UNITS, FARM_TYPES, DEFAULT_FARM_TYPE } from '@/constants/farm-constants';
import { startSpeechRecognition, stopSpeechRecognition, setCurrentField } from '@/services/speech-service';
import { getStoredLanguage, getSpeechLanguageCode } from '@/services/language-service';
import { useAudioFeedback } from '@/hooks/useAudioFeedback';
export default function AddFarmScreen() {
  const { t, language } = useTranslation();
  const router = useRouter();
  const { user } = useAuthStore();
  const { addFarm, isLoading } = useFarmStore();
  const { showToast } = useToast();
  const themedColors = useThemeColors(); // Use the theme hook

  // Immediate check for caretaker role - TRIPLE PROTECTION
  if (!user || !(user.role === 'owner' || user.role === 'admin')) {
    console.log('AddFarmScreen - Unauthorized role detected:', user?.role);
    // Use setTimeout to ensure this runs after component mount
    setTimeout(() => {
      router.replace('/(tabs)');
      Alert.alert(t('common.permissionDenied'), t('common.noAccessToThisFeature'));
    }, 0);
    // Return empty component while redirecting
    return null;
  }

  const styles = getStyles(themedColors, language); // Generate styles with themed colors
  const { playFeedback } = useAudioFeedback();

  const [name, setName] = useState('');
  const [location, setLocation] = useState('');
  const [status, setStatus] = useState<FarmStatus>(FarmStatus.ACTIVE);
  const [photoURL, setPhotoURL] = useState('');
  const [farmType, setFarmType] = useState(DEFAULT_FARM_TYPE);
  const [size, setSize] = useState('');
  const [sizeUnit, setSizeUnit] = useState('acre');
  const [description, setDescription] = useState('');
  const [isRecordingDescription, setIsRecordingDescription] = useState(false);
  const [isProcessingSpeech, setIsProcessingSpeech] = useState(false);
  const [errors, setErrors] = useState({
    name: '',
    location: '',
    size: '',
  });

  // Photo handling functions
  const handleTakePhoto = async () => {
    try {
      const permissionResult = await ImagePicker.requestCameraPermissionsAsync();
      if (!permissionResult.granted) {
        Alert.alert(t('common.permissionRequired'), t('common.cameraPermission'));
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        setPhotoURL(result.assets[0].uri);
      }
    } catch (error) {
      Alert.alert(t('common.error'), t('common.photoError'));
    }
  };

  const handlePickImage = async () => {
    try {
      const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (!permissionResult.granted) {
        Alert.alert(t('common.permissionRequired'), t('common.galleryPermission'));
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        setPhotoURL(result.assets[0].uri);
      }
    } catch (error) {
      Alert.alert(t('common.error'), t('common.photoError'));
    }
  };

  // Voice recording for description
  const startRecordingDescription = async () => {
    try {
      setIsRecordingDescription(true);
      setIsProcessingSpeech(true);

      const appLanguage = await getStoredLanguage();
      const speechLanguage = getSpeechLanguageCode(appLanguage);
      setCurrentField('description');
      await startSpeechRecognition({ language: speechLanguage });
      playFeedback('toggle');
    } catch (error) {
      console.error('Error starting speech recognition:', error);
      Alert.alert(t('common.error'), t('common.speechRecognitionError'));
      setIsProcessingSpeech(false);
      setIsRecordingDescription(false);
      setCurrentField(null);
    }
  };

  const stopRecordingDescription = async () => {
    try {
      const transcription = await stopSpeechRecognition();
      if (transcription && transcription.trim().length > 0) {
        setDescription(prev => prev ? `${prev} ${transcription}` : transcription);
      }
      playFeedback('toggle');
    } catch (error) {
      console.error('Error stopping speech recognition:', error);
      Alert.alert(t('common.error'), t('common.speechRecognitionError'));
    } finally {
      setIsRecordingDescription(false);
      setIsProcessingSpeech(false);
      setCurrentField(null);
    }
  };

  const validateForm = () => {
    let isValid = true;
    const newErrors = {
      name: '',
      location: '',
      size: '',
    };

    if (!name.trim()) {
      newErrors.name = t('farms.nameRequired');
      isValid = false;
    }

    if (!location.trim()) {
      newErrors.location = t('farms.locationRequired');
      isValid = false;
    }

    if (size && isNaN(Number(size))) {
      newErrors.size = t('farms.sizeInvalid');
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  // const handleSave = async () => {
  //   if (!validateForm()) return;
  //   if (!user) return;

  //   try {
  //     const newFarm = await addFarm({
  //       name,
  //       location,
  //       status,
  //       ownerId: user.id,
  //       animalCount: 0,
  //       staffCount: 0,
  //     });

  //     showToast({
  //       type: 'success',
  //       title: t('common.success'),
  //       message: t('farms.addSuccess'),
  //     });

  //     // Navigate to the farm detail screen
  //     router.replace(`/farms/${newFarm.id}`);
  //   } catch (error) {
  //     console.error('Error adding farm:', error);
  //     showToast({
  //       type: 'error',
  //       title: t('common.error'),
  //       message: t('farms.addError'),
  //     });
  //   }
  // };
  const handleSave = async () => {
    if (!validateForm()) return;
    if (!user) return;

    try {
      // Add the farm to Firestore
      const newFarm = await addFarm({
        name,
        location: {
          address: location,
          latitude: 1223.3434, // TODO: Add GPS functionality
          longitude: 2323.2323, // TODO: Add GPS functionality
        },
        status,
        ownerId: user.id,
        photoURL,
        size: size ? Number(size) : undefined,
        sizeUnit,
        type: farmType,
        description,
      });
  
      // Update the owner's assignedFarmIds array
      try {
        const userRef = doc(firestore, 'users', user.id);
        const userDoc = await getDoc(userRef);

        if (userDoc.exists()) {
          const userData = userDoc.data();
          const assignedFarmIds = userData.assignedFarmIds || [];

          // Add the new farm ID to the assignedFarmIds array if it's not already there
          if (!assignedFarmIds.includes(newFarm.id)) {
            await updateDoc(userRef, {
              assignedFarmIds: [...assignedFarmIds, newFarm.id]
            });
            console.log(`Updated user ${user.id} with new farm ${newFarm.id}`);
          }
        }
      } catch (userUpdateError) {
        console.error('Error updating user assignedFarmIds:', userUpdateError);
        // Continue even if user update fails
      }
  
      showToast({
        type: 'success',
        title: t('common.success'),
        message: t('farms.addSuccess'),
      });
  
      // Navigate to the farm detail screen
      router.replace(`/farms/${newFarm.id}`);
    } catch (error) {
      console.error('Error adding farm:', error);
      showToast({
        type: 'error',
        title: t('common.error'),
        message: t('farms.addError'),
      });
    }
  };
  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <Stack.Screen
        options={{
          title: t('farms.addFarm'),
          headerBackTitle: t('common.back'),
        }}
      />
      <ScrollView style={styles.scrollView}>
        <View style={styles.formContainer}>
          {/* Photo Upload */}
          <View style={styles.formGroup}>
            <View style={styles.labelContainer}>
              <Text style={styles.label}>{t('farms.addPhoto')}</Text>
            </View>
            {photoURL ? (
              <View style={styles.imageContainer}>
                <Image source={{ uri: photoURL }} style={styles.farmImage} />
                <TouchableOpacity
                  style={styles.removeImageButton}
                  onPress={() => setPhotoURL('')}
                >
                  <Text style={styles.removeImageText}>×</Text>
                </TouchableOpacity>
              </View>
            ) : (
              <View style={styles.imageUploadCard}>
                <View style={styles.imagePlaceholder}>
                  <Camera size={40} color={themedColors.textSecondary} />
                  <Text style={styles.imagePlaceholderText}>{t('farms.addPhoto')}</Text>
                </View>
                <ImageCaptureButtons
                  onTakePhoto={handleTakePhoto}
                  onChooseFromLibrary={handlePickImage}
                />
              </View>
            )}
          </View>

          {/* Farm Name */}
          <View style={styles.formGroup}>
            <View style={styles.labelContainer}>
              <Text style={styles.label}>{t('farms.name')}*</Text>
            </View>
            <Input
              value={name}
              onChangeText={setName}
              placeholder={t('farms.namePlaceholder')}
              error={errors.name} // @ts-ignore
              leftIcon={<Building2 size={20} color={themedColors.textSecondary} />}
              style={language === 'ur' ? styles.urduText : null}
            />
          </View>

          {/* Farm Type */}
          <View style={styles.formGroup}>
            <View style={styles.labelContainer}>
              <Text style={styles.label}>{t('farms.type')}</Text>
            </View>
            <GenericDropdown
              placeholder={t('farms.selectType')}
              items={FARM_TYPES.map(type => ({
                id: type.id,
                label: type.label,
              }))}
              value={farmType}
              onSelect={setFarmType}
              modalTitle={t('farms.selectType')}
              searchPlaceholder={t('farms.searchTypes')}
              disabled={true} // Disabled as per requirement
            />
          </View>

          {/* Size */}
          <View style={styles.formGroup}>
            <View style={styles.labelContainer}>
              <Text style={styles.label}>{t('farms.size')}</Text>
            </View>
            <Input
              value={size}
              onChangeText={setSize}
              placeholder={t('farms.sizePlaceholder')}
              error={errors.size}
              leftIcon={<Ruler size={20} color={themedColors.textSecondary} />}
              keyboardType="numeric"
              style={language === 'ur' ? styles.urduText : null}
            />
          </View>

          {/* Size Unit */}
          <View style={styles.formGroup}>
            <View style={styles.labelContainer}>
              <Text style={styles.label}>{t('farms.sizeUnit')}</Text>
            </View>
            <GenericDropdown
              placeholder={t('farms.selectUnit')}
              items={FARM_SIZE_UNITS.map(unit => ({
                id: unit.id,
                label: unit.label,
              }))}
              value={sizeUnit}
              onSelect={setSizeUnit}
              modalTitle={t('farms.selectUnit')}
              searchPlaceholder={t('farms.searchUnits')}
            />
          </View>

          {/* Location */}
          <View style={styles.formGroup}>
            <View style={styles.labelContainer}>
              <Text style={styles.label}>{t('farms.location')}*</Text>
            </View>
            <Input
              value={location}
              onChangeText={setLocation}
              placeholder={t('farms.locationPlaceholder')}
              error={errors.location} // @ts-ignore
              leftIcon={<MapPin size={20} color={themedColors.textSecondary} />}
              style={language === 'ur' ? styles.urduText : null}
            />
          </View>

          {/* Description with Voice */}
          <View style={styles.formGroup}>
            <View style={styles.labelContainer}>
              <FileText size={24} color={themedColors.primary} />
              <Text style={[styles.label, language === 'ur' ? styles.urduText : null]}>
                {t('farms.description')}
              </Text>
              {Platform.OS !== 'web' && (
                <TouchableOpacity
                  style={[
                    styles.voiceButtonHeader,
                    isRecordingDescription && styles.voiceButtonRecording
                  ]}
                  onPress={() => {
                    if (isRecordingDescription) {
                      stopRecordingDescription();
                    } else {
                      startRecordingDescription();
                    }
                  }}
                >
                  {isRecordingDescription ? (
                    <MicOff size={20} color={themedColors.error} />
                  ) : (
                    <Mic size={20} color={themedColors.primary} />
                  )}
                </TouchableOpacity>
              )}
            </View>
            <Input
              value={description}
              onChangeText={setDescription}
              placeholder={t('farms.descriptionPlaceholder')}
              multiline
              numberOfLines={4}
              style={[
                styles.textArea,
                language === 'ur' ? styles.urduText : null
              ]}
            />
            {isProcessingSpeech && (
              <View style={styles.speechProcessing}>
                <ActivityIndicator size="small" color={themedColors.primary} />
                <Text style={styles.speechProcessingText}>
                  {t('common.processingSpeech')}
                </Text>
              </View>
            )}
          </View>

          <View style={styles.formGroup}>
            <View style={styles.labelContainer}>
              <Text style={styles.label}>{t('farms.status')}</Text>
            </View>
            <View style={[
              styles.statusButtons,
              language === 'ur' && { flexDirection: 'row-reverse' }
            ]}>
              <TouchableOpacity
                style={[
                  styles.statusButton,
                  status === FarmStatus.ACTIVE && styles.statusButtonSelected
                ]}
                onPress={() => setStatus(FarmStatus.ACTIVE)}
              >
                <Text style={[
                  styles.statusButtonText,
                  status === FarmStatus.ACTIVE && styles.statusButtonTextSelected,
                  language === 'ur' ? styles.urduText : null
                ]}>
                  {t('farms.statusActive')}
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.statusButton,
                  status === FarmStatus.INACTIVE && styles.statusButtonSelected
                ]}
                onPress={() => setStatus(FarmStatus.INACTIVE)}
              >
                <Text style={[
                  styles.statusButtonText,
                  status === FarmStatus.INACTIVE && styles.statusButtonTextSelected,
                  language === 'ur' ? styles.urduText : null
                ]}>
                  {t('farms.statusInactive')}
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.statusButton,
                  status === FarmStatus.PENDING && styles.statusButtonSelected
                ]}
                onPress={() => setStatus(FarmStatus.PENDING)}
              >
                <Text style={[
                  styles.statusButtonText,
                  status === FarmStatus.PENDING && styles.statusButtonTextSelected,
                  language === 'ur' ? styles.urduText : null
                ]}>
                  {t('farms.statusPending')}
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          <View style={styles.buttonContainer}>
            <Button
              title={t('farms.saveFarm')}
              onPress={handleSave}
              isLoading={isLoading}
              leftIcon={<Save size={20} color="white" />}
            />
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const getStyles = (themedColors: ReturnType<typeof useThemeColors>, language: string) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: themedColors.background,
  },
  scrollView: {
    flex: 1,
  },
  formContainer: {
    padding: 16,
    paddingTop: 24, // Add some top padding for better spacing
  },
  formGroup: {
    marginBottom: 20, // Increased margin for better separation
  },
  labelContainer: {
    marginBottom: 8,
    flexDirection: language === 'ur' ? 'row-reverse' : 'row', // Adjust for Urdu
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    color: themedColors.text,
    textAlign: language === 'ur' ? 'right' : 'left', // Adjust for Urdu
  },
  statusButtons: {
    flexDirection: 'row',
    justifyContent: language === 'ur' ? 'flex-end' : 'flex-start', // Adjust for Urdu
    flexWrap: 'wrap',
  },
  statusButton: {
    backgroundColor: themedColors.card,
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginRight: language === 'ur' ? 0 : 8, // Adjust for Urdu
    marginLeft: language === 'ur' ? 8 : 0, // Adjust for Urdu
    marginBottom: 8,
    borderWidth: 1,
    borderColor: themedColors.border,
  },
  statusButtonSelected: {
    backgroundColor: themedColors.primary,
    borderColor: themedColors.primary,
  },
  statusButtonText: {
    color: themedColors.text,
    fontSize: 14,
    fontWeight: '500',
  },
  statusButtonTextSelected: {
    color: 'white',
  },
  buttonContainer: {
    marginTop: 32, // Increased margin for the button
  },
  // Image upload styles
  imageContainer: {
    position: 'relative',
    marginBottom: 16,
  },
  farmImage: {
    width: '100%',
    height: 200,
    borderRadius: 12,
    backgroundColor: themedColors.card,
  },
  removeImageButton: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: themedColors.error,
    borderRadius: 16,
    width: 32,
    height: 32,
    justifyContent: 'center',
    alignItems: 'center',
  },
  removeImageText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  imageUploadCard: {
    backgroundColor: themedColors.card,
    borderRadius: 12,
    padding: 20,
    alignItems: 'center',
    borderWidth: 2,
    borderColor: themedColors.border,
    borderStyle: 'dashed',
  },
  imagePlaceholder: {
    alignItems: 'center',
    marginBottom: 16,
  },
  imagePlaceholderText: {
    color: themedColors.textSecondary,
    fontSize: 16,
    marginTop: 8,
    textAlign: 'center',
  },
  // Voice recording styles
  voiceButtonHeader: {
    backgroundColor: themedColors.card,
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: language === 'ur' ? 0 : 8,
    marginRight: language === 'ur' ? 8 : 0,
    borderWidth: 1,
    borderColor: themedColors.border,
  },
  voiceButtonRecording: {
    backgroundColor: themedColors.error + '20',
    borderColor: themedColors.error,
  },
  textArea: {
    minHeight: 100,
    textAlignVertical: 'top',
  },
  speechProcessing: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
    paddingHorizontal: 12,
  },
  speechProcessingText: {
    color: themedColors.textSecondary,
    fontSize: 14,
    marginLeft: 8,
  },
  urduText: {
    fontFamily: 'System',
    textAlign: 'right',
  },
});
