import { firestore } from '@/config/firebase';
import { collection, getDocs, query, where, doc, updateDoc, deleteDoc, getDoc, setDoc } from 'firebase/firestore';

export interface Employee {
  id?: string;
  name: string;
  email: string;
  phone_number: string;
  cnic: string;
  age: number;
  gender: string;
  role: string;
  joining_date: Date;
  status: string;
  ownerId: string;
  farmId?: string;
  farmIds?: string[]; // Array of farm IDs this employee has access to
  assignedFarms?: string[]; // Array of farm IDs this employee is assigned to
  password?: string;
  photo?: string; // URL to employee photo
  // Multi-tenancy fields
  tenantId?: string; // ID of the owner (tenant) who owns the farm this employee is associated with
  uid?: string; // Firebase Auth UID
  // Timestamps
  created_at?: Date;
  updated_at?: Date;
  createdAt?: Date; // Alternative timestamp field name
  isEmployee?: boolean; // Flag to identify as employee
}

export const addEmployee = async (employeeData: Partial<Employee>): Promise<string> => {
  try {
    // Create the employee document
    const employeeRef = doc(collection(firestore, 'users'));
    const employeeId = employeeRef.id;
    
    // Set default values and add ID
    const newEmployee = {
      ...employeeData,
      id: employeeId,
      role: employeeData.role || 'caretaker',
      status: employeeData.status || 'active',
      created_at: new Date(),
      updated_at: new Date(),
    };
    
    // Ensure assignedFarms matches farmIds
    if (employeeData.farmIds) {
      newEmployee.assignedFarms = employeeData.farmIds;
    }
    
    await setDoc(employeeRef, newEmployee);

    
    // Note: Staff is now managed through Users collection only
    // assignedFarmIds are stored in the user document
    
    return employeeId;
  } catch (error) {
    throw error;
  }
};

export const getEmployeesByOwner = async (ownerId: string) => {
  try {
    const q = query(collection(firestore, 'users'), 
      where('ownerId', '==', ownerId),
      where('isEmployee', '==', true));
    const querySnapshot = await getDocs(q);

    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
    }));
  } catch (error) {
    throw error;
  }
};

export const getEmployeesByFarm = async (farmId: string): Promise<Employee[]> => {
  try {
    // Use the new staff service to get staff from Users collection
    const usersRef = collection(firestore, 'users');
    const q = query(usersRef, where('assignedFarmIds', 'array-contains', farmId));
    const querySnapshot = await getDocs(q);

    const employees: Employee[] = [];
    querySnapshot.forEach((doc) => {
      const userData = doc.data();
      employees.push({
        id: doc.id,
        ...userData,
      } as Employee);
    });

    return employees;
  } catch (error) {
    throw error;
  }
};

export const updateEmployee = async (id: string, data: Partial<Employee>): Promise<void> => {
  try {
    const employeeRef = doc(firestore, 'users', id);
    
    // Note: Staff is now managed through Users collection only
    // assignedFarmIds are stored in the user document
    
    // Update the employee document
    await updateDoc(employeeRef, {
      ...data,
      updated_at: new Date()
    });
    
  } catch (error) {
    throw error;
  }
};

export const updateEmployeeStatus = async (id: string, status: 'active' | 'inactive') => {
  try {
    const userRef = doc(firestore, 'users', id);
    
    // Update only the status field
    await updateDoc(userRef, {
      status,
      updated_at: Date.now()
    });

    return {
      id,
      status
    };
  } catch (error) {
    throw error;
  }
};

export const deleteEmployee = async (id: string) => {
  try {
    // First, get the employee data to find the farmId
    const userRef = doc(firestore, 'users', id);
    const userDoc = await getDoc(userRef);

    if (!userDoc.exists()) {
      throw new Error('Employee not found');
    }

    // Delete the user document
    await deleteDoc(userRef);

    // Note: Staff is now managed through Users collection only
    // No need to update farm staff arrays

    return id;
  } catch (error) {
    throw error;
  }
};

// syncFarmStaffCount function removed - staff count is now calculated dynamically from Users collection

export const getEmployeeById = async (id: string): Promise<Employee> => {
  try {
    const userRef = doc(firestore, 'users', id);
    const userDoc = await getDoc(userRef);
    
    if (!userDoc.exists()) {
      throw new Error('Employee not found');
    }
    
    const userData = userDoc.data();

    // Ensure farmIds is properly extracted and formatted
    let farmIds = userData.farmIds || userData.assignedFarms || [];
    
    // If farmIds is not an array, convert it to an array
    if (farmIds && !Array.isArray(farmIds)) {
      farmIds = [farmIds];
    }
    
    // Ensure assignedFarms is properly extracted and formatted
    let assignedFarms = userData.assignedFarms || userData.farmIds || [];
    
    // If assignedFarms is not an array, convert it to an array
    if (assignedFarms && !Array.isArray(assignedFarms)) {
      assignedFarms = [assignedFarms];
    }

    return {
      id: userDoc.id,
      ...userData,
      farmIds: farmIds,
      assignedFarms: assignedFarms,
    } as Employee;
  } catch (error) {
    throw error;
  }
};

