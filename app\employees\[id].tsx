import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  FlatList,
  Image,
  ScrollView,
} from 'react-native';
import { useLocalSearchParams, useRouter, Stack } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { colors } from '@/constants/colors';
import { useAuthStore } from '@/store/auth-store';
import { useTranslation } from '@/hooks/useTranslation';
import { useThemeColors } from '@/hooks/useThemeColors';
import { getEmployeeById, updateEmployeeStatus, deleteEmployee, updateEmployee, Employee } from '@/services/employee-service';
import { useFarmStore } from '@/store/farm-store';
import { User, Mail, Phone, CreditCard, Calendar, Clock, MapPin, Plus } from 'lucide-react-native';
import LoadingIndicator from '@/components/LoadingIndicator';
import EmptyState from '@/components/EmptyState';
import MultiSelectDropdown from '@/components//MultiSelectDropdown';
import { doc, getDoc, updateDoc } from 'firebase/firestore';
import { firestore } from '@/config/firebase';

export default function EmployeeDetailScreen() {
  const { t } = useTranslation();
  const { id } = useLocalSearchParams<{ id: string }>();
  const router = useRouter();
  const { user } = useAuthStore();
  const { farms } = useFarmStore();
  const themedColors = useThemeColors();
  
  const [employee, setEmployee] = useState<Employee | null>(null);
  const [loading, setLoading] = useState(true);
  const [assignedFarms, setAssignedFarms] = useState<any[]>([]);
  const [selectedFarmIds, setSelectedFarmIds] = useState<string[]>([]);
  const [showFarmSelector, setShowFarmSelector] = useState(false);

  useEffect(() => {
    const loadEmployeeData = async () => {
      try {
        setLoading(true);
        const data = await getEmployeeById(id);
        setEmployee(data);
        
        // Get the farms this employee has access to
        if (data) {
          // Check all possible properties where farm IDs might be stored (with backward compatibility)
          const farmIdList = data.assignedFarmIds || [];
          console.log("Farm IDs found:", farmIdList); // Debug log
          
          if (farmIdList.length > 0) {
            // Filter the farms from farmStore that match the employee's farm IDs
            const employeeFarms = farms.filter(farm => 
              farmIdList.includes(farm.id)
            );
            console.log("Matched farms:", employeeFarms); // Debug log
            setAssignedFarms(employeeFarms);
            setSelectedFarmIds(farmIdList);
          } else {
            console.log("No farm IDs found for employee"); // Debug log
            setAssignedFarms([]);
            setSelectedFarmIds([]);
          }
        }
      } catch (error) {
        console.error('Error loading employee:', error);
        Alert.alert(t('common.error'), t('common.errorOccurred'));
      } finally {
        setLoading(false);
      }
    };
    
    if (id) {
      loadEmployeeData();
    }
  }, [id, farms]);

  const handleEdit = () => {
    router.push(`/employees/${id}/edit`);
  };

  const handleDeactivate = async () => {
    if (!employee) return;

    const newStatus = employee.status === 'active' ? 'inactive' : 'active';
    const actionText = newStatus === 'active' ? t('common.activate') : t('common.deactivate');

    Alert.alert(
      t('common.confirm'),
      `${actionText} ${employee.name}?`,
      [
        {
          text: t('common.cancel'),
          style: 'cancel',
        },
        {
          text: t('common.yes'),
          onPress: async () => {
            try {
              setLoading(true);
              await updateEmployeeStatus(employee.id!, newStatus);
              setEmployee({ ...employee, status: newStatus });
              Alert.alert(
                t('common.success'),
                `${employee.name} ${newStatus === 'active' ? t('common.activated') : t('common.deactivated')}`
              );
            } catch (error) {
              console.error('Error updating employee status:', error);
              Alert.alert(t('common.error'), t('farms.staffSection.updateError'));
            } finally {
              setLoading(false);
            }
          },
        },
      ]
    );
  };

  const handleDelete = () => {
    if (!employee) return;

    Alert.alert(
      t('common.confirm'),
      `${t('common.delete')} ${employee.name}?`,
      [
        {
          text: t('common.cancel'),
          style: 'cancel',
        },
        {
          text: t('common.delete'),
          style: 'destructive',
          onPress: async () => {
            try {
              setLoading(true);
              await deleteEmployee(employee.id!);
              Alert.alert(
                t('common.success'),
                `${employee.name} ${t('common.deleted')}`,
                [
                  {
                    text: t('common.ok'),
                    onPress: () => router.back(),
                  },
                ]
              );
            } catch (error) {
              console.error('Error deleting employee:', error);
              Alert.alert(t('common.error'), t('farms.staffSection.deleteError'));
              setLoading(false);
            }
          },
        },
      ]
    );
  };

  // Format date function to handle invalid dates and Firestore Timestamps
  const formatDate = (date: any) => {
    if (!date) return "Not set";
    
    let timestamp: number;
    
    // Handle Firestore Timestamp objects
    if (date && typeof date === 'object' && 'seconds' in date) {
      timestamp = date.seconds * 1000; // Convert seconds to milliseconds
    } 
    // Handle regular Date objects
    else if (date instanceof Date) {
      timestamp = date.getTime();
    } 
    // Handle timestamp numbers
    else if (typeof date === 'number') {
      timestamp = date;
    } 
    else {
      return "Not set";
    }
    
    // Create a Date object from the timestamp
    const dateObj = new Date(timestamp);
    
    // Check if date is valid
    if (isNaN(dateObj.getTime())) return "Not set";
    
    // Format the date
    return dateObj.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const handleFarmSelectionChange = async (selectedIds: string[]) => {
    setSelectedFarmIds(selectedIds);
    
    if (!employee) return;
    
    try {
      setLoading(true);
      
      // Update employee with new assignedFarmIds (using old field name for compatibility)
      await updateEmployee(employee.id!, {
        assignedFarmIds: selectedIds,
        updated_at: new Date()
      } as any);

      // Update local state
      setEmployee({
        ...employee,
        assignedFarmIds: selectedIds
      } as any);
      
      // Update assigned farms list
      const newAssignedFarms = farms.filter(farm => selectedIds.includes(farm.id));
      setAssignedFarms(newAssignedFarms);
      
      // Show success message
      Alert.alert(
        t('common.success'),
        t('farms.staffSection.farmsUpdated')
      );
    } catch (error) {
      console.error('Error updating employee farms:', error);
      Alert.alert(t('common.error'), t('farms.staffSection.updateError'));
    } finally {
      setLoading(false);
      setShowFarmSelector(false);
    }
  };
  
  useEffect(() => {
    if (employee && employee.farmIds) {
      setSelectedFarmIds(employee.farmIds);
    }
  }, [employee]);

  if (loading) {
    return <LoadingIndicator fullScreen message={t('common.loading')} />;
  }

  if (!employee) {
    return (
      <EmptyState
        title={t('farms.staffSection.notFound')}
        message={t('farms.staffSection.notFoundMessage')}
        actionLabel={t('common.goBack')}
        onAction={() => router.back()}
        icon={<User size={48} color={themedColors.primary} />}
      />
    );
  }

  const styles = getStyles(themedColors);

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <Stack.Screen
        options={{
          title: t('farms.staffSection.employeeDetails'),
          headerBackTitle: t('common.back'),
        }}
      />

      <ScrollView style={styles.scrollView}>
        <View style={styles.header}>
          <View style={styles.statusContainer}>
            <View style={[
              styles.statusBadge,
              employee.status === 'active' ? styles.statusActive : styles.statusInactive
            ]}>
              <View style={styles.statusDot} />
              <Text style={styles.statusText}>
                {employee.status === 'active' ? t('common.active') : t('common.inactive')}
              </Text>
            </View>
          </View>

          <View style={styles.profileSection}>
            <View style={styles.avatarContainer}>
              {employee.photo ? (
                <Image source={{ uri: employee.photo }} style={styles.avatar} />
              ) : (
                <View style={styles.avatarPlaceholder}>
                  <Text style={styles.avatarText}>
                    {employee.name.substring(0, 2).toUpperCase()}
                  </Text>
                </View>
              )}
            </View>

            <View style={styles.nameContainer}>
              <Text style={styles.name}>{employee.name}</Text>
              <View style={styles.genderAgeContainer}>
                <User size={14} color={themedColors.textSecondary} style={styles.infoIcon} />
                <Text style={styles.genderAge}>
                  {employee.gender === 'male' ? t('common.male') : t('common.female')}, {employee.age} {t('common.years')}
                </Text>
              </View>
            </View>
          </View>

          <View style={styles.infoSection}>
            <View style={styles.infoRow}>
              <Mail size={16} color={themedColors.textSecondary} style={styles.infoIcon} />
              <Text style={styles.infoText}>{employee.email || t('common.noEmail')}</Text>
            </View>

            <View style={styles.infoRow}>
              <Phone size={16} color={themedColors.textSecondary} style={styles.infoIcon} />
              <Text style={styles.infoText}>{employee.phone_number || t('common.noPhone')}</Text>
            </View>

            <View style={styles.infoRow}>
              <CreditCard size={16} color={themedColors.textSecondary} style={styles.infoIcon} />
              <Text style={styles.infoText}>CNIC: {employee.cnic || t('common.notAvailable')}</Text>
            </View>

            {/* Add spacing before role badge */}
            <View style={{ marginTop: 1 }}></View>

            <View style={styles.roleBadge}>
              <Text style={styles.roleText}>{t(`farms.staffSection.roles.${employee.role.toLowerCase()}`)}</Text>
            </View>
          </View>
        </View>

        {/* Farm Access Section */}
        <View style={styles.detailsSection}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>{t('farms.accessibleFarms')}</Text>
            <TouchableOpacity
              style={styles.addButton}
              onPress={() => setShowFarmSelector(true)}
            >
              <Plus size={20} color={themedColors.primary} />
            </TouchableOpacity>
          </View>
          
          {assignedFarms.length === 0 ? (
            <View style={styles.noFarmsContainer}>
              <Text style={styles.noFarmsText}>{t('farms.noAssignedFarms')}</Text>
            </View>
          ) : (
            assignedFarms.map(farm => (
              <TouchableOpacity 
                key={farm.id} 
                style={styles.farmItem}
                onPress={() => router.push(`/farms/${farm.id}`)}
              >
                <MapPin size={20} color={themedColors.primary} style={styles.detailIcon} />
                <View style={styles.detailContent}>
                  <Text style={styles.farmName}>{farm.name}</Text>
                  <Text style={styles.farmLocation}>
                    {typeof farm.location === 'string' ? farm.location : farm.location?.address || 'No location'}
                  </Text>
                </View>
              </TouchableOpacity>
            ))
          )}
        </View>
        
        {/* Farm Selector Modal */}
        {showFarmSelector && (
          <View style={styles.farmSelectorContainer}>
            <Text style={styles.farmSelectorTitle}>{t('farms.selectFarms')}</Text>
            <MultiSelectDropdown
              items={farms.map(farm => ({
                id: farm.id,
                label: farm.name,
                description: typeof farm.location === 'string' ? farm.location : farm.location?.address || 'No location',
                icon: <MapPin size={20} color={themedColors.primary} />
              }))}
              selectedIds={selectedFarmIds}
              onSelectionChange={handleFarmSelectionChange}
              placeholder={t('farms.selectFarmsPlaceholder')}
            />
            <View style={styles.farmSelectorButtons}>
              <TouchableOpacity 
                style={[styles.farmSelectorButton, styles.cancelButton]}
                onPress={() => setShowFarmSelector(false)}
              >
                <Text style={styles.cancelButtonText}>{t('common.cancel')}</Text>
              </TouchableOpacity>
              <TouchableOpacity 
                style={[styles.farmSelectorButton, styles.saveButton]}
                onPress={() => handleFarmSelectionChange(selectedFarmIds)}
              >
                <Text style={styles.saveButtonText}>{t('common.save')}</Text>
              </TouchableOpacity>
            </View>
          </View>
        )}

        <View style={styles.detailsSection}>
          <View style={styles.detailItem}>
            <Calendar size={20} color={themedColors.textSecondary} style={styles.detailIcon} />
            <View style={styles.detailContent}>
              <Text style={styles.detailLabel}>{t('farms.staffSection.joiningDate')}</Text>
              <Text style={styles.detailValue}>{formatDate(employee.joining_date)}</Text>
            </View>
          </View>

          <View style={styles.detailItem}>
            <Clock size={20} color={themedColors.textSecondary} style={styles.detailIcon} />
            <View style={styles.detailContent}>
              <Text style={styles.detailLabel}>{t('common.createdAt')}</Text>
              <Text style={styles.detailValue}>{formatDate(employee.createdAt || employee.created_at)}</Text>
            </View>
          </View>

          <View style={styles.detailItem}>
            <Clock size={20} color={themedColors.textSecondary} style={styles.detailIcon} />
            <View style={styles.detailContent}>
              <Text style={styles.detailLabel}>{t('common.lastUpdated')}</Text>
              <Text style={styles.detailValue}>{formatDate(employee.updatedAt || employee.updated_at)}</Text>
            </View>
          </View>
        </View>

        <View style={styles.actionButtons}>
          <TouchableOpacity 
            style={[styles.actionButton, styles.editButton]} 
            onPress={handleEdit}
          >
            <Text style={styles.actionButtonText}>{t('common.edit')}</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[styles.actionButton, styles.deactivateButton]} 
            onPress={handleDeactivate}
          >
            <Text style={styles.deactivateButtonText}>
              {employee.status === 'active' ? t('common.deactivate') : t('common.activate')}
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const getStyles = (themedColors: ReturnType<typeof useThemeColors>) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: themedColors.background,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    backgroundColor: themedColors.card,
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: themedColors.border,
    position: 'relative',
  },
  statusContainer: {
    position: 'absolute',
    top: 16,
    right: 16,
    zIndex: 1,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 16,
  },
  statusActive: {
    backgroundColor: '#DCFCE7', // Light green
  },
  statusInactive: {
    backgroundColor: '#F3F4F6', // Light gray
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#10B981', // Green
    marginRight: 6,
  },
  statusText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#10B981', // Green
  },
  profileSection: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    marginTop: 8,
  },
  avatarContainer: {
    marginRight: 16,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
  },
  avatarPlaceholder: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#E5E7EB',
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    fontSize: 28,
    fontWeight: 'bold',
    color: themedColors.textSecondary,
  },
  nameContainer: {
    flex: 1,
  },
  name: {
    fontSize: 24,
    fontWeight: 'bold',
    color: themedColors.text,
    marginBottom: 4,
  },
  genderAgeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  genderAge: {
    fontSize: 14,
    color: themedColors.textSecondary,
  },
  infoSection: {
    marginBottom: 16,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  infoIcon: {
    marginRight: 8,
  },
  infoText: {
    fontSize: 14,
    color: themedColors.textSecondary,
  },
  roleBadge: {
    backgroundColor: themedColors.primaryLight,
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 16,
    alignSelf: 'flex-start',
    marginTop: 12, // Add top margin
  },
  roleText: {
    fontSize: 14,
    fontWeight: '500',
    color: themedColors.primary,
  },
  detailsSection: {
    backgroundColor: themedColors.card,
    marginTop: 16,
    padding: 16,
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderColor: themedColors.border,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  detailIcon: {
    marginRight: 12,
  },
  detailContent: {
    flex: 1,
  },
  detailLabel: {
    fontSize: 14,
    color: themedColors.textSecondary,
    marginBottom: 2,
  },
  detailValue: {
    fontSize: 14,
    fontWeight: '500',
    color: themedColors.text,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
    paddingHorizontal: 16,
    marginBottom: 20,
  },
  actionButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 8,
  },
  editButton: {
    backgroundColor: themedColors.primary,
  },
  deactivateButton: {
    backgroundColor: themedColors.border,
    borderWidth: 1,
    borderColor: themedColors.textSecondary,
  },
  actionButtonText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 16,
  },
  deactivateButtonText: {
    color: themedColors.textSecondary,
    fontWeight: '600',
    fontSize: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: themedColors.text,
    marginBottom: 12,
  },
  noFarmsContainer: {
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  noFarmsText: {
    color: themedColors.textSecondary,
    fontSize: 14,
  },
  farmItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: themedColors.border,
  },
  farmName: {
    fontSize: 16,
    fontWeight: '500',
    color: themedColors.text,
  },
  farmLocation: {
    fontSize: 14,
    color: themedColors.textSecondary,
    marginTop: 2,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  addButton: {
    padding: 4,
  },
  farmSelectorContainer: {
    backgroundColor: themedColors.card,
    marginTop: 16,
    padding: 16,
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderColor: themedColors.border,
  },
  farmSelectorTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: themedColors.text,
    marginBottom: 12,
  },
  farmSelectorButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  farmSelectorButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 8,
  },
  cancelButton: {
    backgroundColor: themedColors.border,
  },
  saveButton: {
    backgroundColor: themedColors.primary,
  },
  cancelButtonText: {
    color: themedColors.text,
    fontWeight: '600',
    fontSize: 16,
  },
  saveButtonText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 16,
  },
});

























